{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "setup:dev": "node setup.js development", "setup:prod": "node setup.js production", "config:wallet": "node setup-marketplace-wallet.js", "fix:iam": "bash ../fix-iam-permissions.sh", "fix:firebase": "bash ../fix-firebase-permissions.sh"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@google-cloud/logging-winston": "^6.0.1", "@mikerudenko/marketplace-shared": "git+https://<EMAIL>/miker<PERSON><PERSON>/marketplace-shared.git#v1.2.12", "@orbs-network/ton-access": "github:orbs-network/ton-access", "@telegram-apps/sdk": "^3.10.1", "@ton/core": "^0.61.0", "@ton/crypto": "^3.3.0", "@ton/ton": "^15.3.0", "crypto-js": "^4.2.0", "dotenv": "^17.0.0", "firebase-admin": "^12.1.0", "firebase-functions": "^6.3.2", "node-fetch": "^2.7.0", "telegram": "^2.26.22", "winston": "^3.17.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node-fetch": "^2.6.12", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^5.0.0"}, "private": true}