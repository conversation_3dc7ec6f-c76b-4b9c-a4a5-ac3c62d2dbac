import * as admin from "firebase-admin";
import { HttpsError } from "firebase-functions/v2/https";
import { hasAvailableBalance } from "./balance-service";
import { getAppConfig, calculateFeeAmount } from "./fee-service";

import { safeMultiply } from "../utils";
import {
  CollectionEntity,
  OrderEntity,
  OrderFees,
  OrderStatus,
  UserType,
} from "@mikerudenko/marketplace-shared";

export async function validateCollectionAndFloorPrice(params: {
  db: admin.firestore.Firestore;
  collectionId: string;
  amount: number;
}) {
  const { db, collectionId, amount } = params;
  const collectionDoc = await db
    .collection("collections")
    .doc(collectionId)
    .get();

  if (!collectionDoc.exists) {
    throw new HttpsError("not-found", "Collection not found.");
  }

  const collection = collectionDoc.data() as CollectionEntity;

  // Check if collection is active
  if (collection.active === false) {
    throw new HttpsError(
      "failed-precondition",
      "This collection is not active for order creation."
    );
  }

  if (amount < collection?.floorPrice) {
    throw new HttpsError(
      "invalid-argument",
      `Order amount must be at least ${collection?.floorPrice} TON (collection floor price).`
    );
  }

  return collection;
}

export async function validateBalanceAndCalculateLock(params: {
  userId: string;
  amount: number;
  userType: UserType;
  orderFees?: OrderFees;
}) {
  const { userId, amount, userType, orderFees } = params;
  let lockPercentageBPS: number;

  if (orderFees) {
    // Use fees from existing order
    lockPercentageBPS =
      userType === UserType.BUYER
        ? orderFees.buyer_locked_percentage
        : orderFees.seller_locked_percentage;
  } else {
    // Use current app config for new orders
    const config = await getAppConfig();
    lockPercentageBPS =
      userType === UserType.BUYER
        ? config?.buyer_lock_percentage
        : config?.seller_lock_percentage;
  }

  // Convert BPS to decimal (divide by 10000)
  const lockPercentage = lockPercentageBPS / 10000;
  const lockedAmount = safeMultiply(amount, lockPercentage);

  // For buyers, also validate they have sufficient balance for purchase fees
  let totalRequiredAmount = lockedAmount;
  let purchaseFeeAmount = 0;

  if (userType === UserType.BUYER) {
    let purchaseFeeBPS = 0;

    if (orderFees) {
      // Use fees from existing order
      purchaseFeeBPS = orderFees.purchase_fee ?? 0;
    } else {
      // Use current app config for new orders
      const config = await getAppConfig();
      purchaseFeeBPS = config?.purchase_fee ?? 0;
    }

    if (purchaseFeeBPS > 0) {
      purchaseFeeAmount = calculateFeeAmount(amount, purchaseFeeBPS);
      totalRequiredAmount = lockedAmount + purchaseFeeAmount;
    }
  }

  const hasBalance = await hasAvailableBalance(userId, totalRequiredAmount);
  if (!hasBalance) {
    const feeMessage =
      purchaseFeeAmount > 0 ? ` + ${purchaseFeeAmount} TON purchase fee` : "";

    throw new HttpsError(
      "failed-precondition",
      `Insufficient balance. You need at least ${totalRequiredAmount} TON available (${lockedAmount} TON collateral${feeMessage}).`
    );
  }

  return { lockedAmount, lockPercentage };
}

export async function validateOrderCreation(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    collectionId: string;
    price: number;
    userType: UserType;
  }
) {
  const { userId, collectionId, price, userType } = params;

  const collection = await validateCollectionAndFloorPrice({
    db,
    collectionId,
    amount: price,
  });
  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: price,
      userType,
    });

  return { collection, lockedAmount, lockPercentage };
}

export async function getAndValidateOrder(
  db: admin.firestore.Firestore,
  orderId: string
): Promise<OrderEntity> {
  const orderDoc = await db.collection("orders").doc(orderId).get();

  if (!orderDoc.exists) {
    throw new HttpsError("not-found", "Order not found.");
  }

  return { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;
}

export function validateOrderAvailableForPurchase(order: OrderEntity): void {
  if (order.status !== OrderStatus.ACTIVE) {
    throw new HttpsError(
      "failed-precondition",
      "Order is not available for purchase."
    );
  }
}

export function validateBuyerPurchaseConstraints(
  order: OrderEntity,
  buyerId: string
) {
  // Check if order already has a buyer
  if (order.buyerId && order.buyerId !== buyerId) {
    throw new HttpsError("failed-precondition", "Order already has a buyer.");
  }

  // Check if buyer is trying to buy their own order
  if (order.sellerId === buyerId) {
    throw new HttpsError(
      "failed-precondition",
      "You cannot buy your own order."
    );
  }
}

export function validateSellerPurchaseConstraints(
  order: OrderEntity,
  sellerId: string
) {
  // Check if order already has a seller
  if (order.sellerId && order.sellerId !== sellerId) {
    throw new HttpsError("failed-precondition", "Order already has a seller.");
  }

  // Check if seller is trying to sell to their own order
  if (order.buyerId === sellerId) {
    throw new HttpsError(
      "failed-precondition",
      "You cannot sell to your own order."
    );
  }
}

export async function validateBuyerPurchase(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    orderId: string;
  }
) {
  const { userId, orderId } = params;
  const order = await getAndValidateOrder(db, orderId);

  validateOrderAvailableForPurchase(order);
  validateBuyerPurchaseConstraints(order, userId);

  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: order.price,
      userType: UserType.BUYER,
      orderFees: order.fees,
    });

  return { order, lockedAmount, lockPercentage };
}

export async function validateSellerPurchase(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    orderId: string;
  }
) {
  const { userId, orderId } = params;
  const order = await getAndValidateOrder(db, orderId);

  validateOrderAvailableForPurchase(order);
  validateSellerPurchaseConstraints(order, userId);

  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock({
      userId,
      amount: order.price,
      userType: UserType.SELLER,
      orderFees: order.fees,
    });

  return { order, lockedAmount, lockPercentage };
}
